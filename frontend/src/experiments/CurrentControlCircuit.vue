<template>
  <div class="experiment-page">
    <!-- 实验标题 -->
    <header class="experiment-header">
      <h1>制流电路实验</h1>
      <p>通过测量不同接入比例下的电流变化，掌握制流电路的工作原理和特性</p>
    </header>

    <!-- 实验内容 -->
    <!-- 统一导航：步骤指示器 -->
    <StepIndicator :steps="steps" :current-step="currentStep" :current-substep="currentSubstep" />

    <main class="experiment-content">
      <!-- 步骤1: 实验准备 -->
      <section class="step-section" v-if="currentStep === 1">
        <h2>步骤1: 实验准备</h2>
        <div class="step-content">
          <h3>实验目的</h3>
          <ul>
            <li>掌握制流电路的基本原理</li>
            <li>学会测量不同接入比例下的电流变化</li>
            <li>分析电流与接入比例的关系</li>
          </ul>

          <h3>实验器材</h3>
          <ul>
            <li>制流电路实验板</li>
            <li>数字万用表</li>
            <li>导线若干</li>
          </ul>

          <h3>注意事项</h3>
          <ul>
            <li>实验前检查电路连接是否正确</li>
            <li>测量时注意万用表的量程选择</li>
            <li>记录数据时保持精确度</li>
          </ul>
        </div>
        <ExperimentNavigation
          :current-step="currentStep"
          :total-steps="totalSteps"
          :current-substep="currentSubstep"
          :total-substeps="totalSubsteps"
          @next="goNext"
          :show-prev="false"
        />
      </section>

      <!-- 步骤2: k=1数据测量 -->
      <section class="step-section" v-if="currentStep === 2">
        <h2>步骤2: k=1 电流测量</h2>
        <div class="step-content">
          <p>请按照以下接入比例测量电流值，并填入表格：</p>

          <div class="data-table">
            <h3>k=1 测量数据</h3>
            <table>
              <thead>
                <tr>
                  <th>接入比例</th>
                  <th>电流值 (mA)</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in k1Data" :key="index">
                  <td>{{ item.ratio }}</td>
                  <td>
                    <el-input
                      v-model.number="item.current"
                      type="number"
                      placeholder="请输入电流值"
                      :min="0"
                      :max="50"
                      size="small"
                    />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <ExperimentNavigation
          :current-step="currentStep"
          :total-steps="totalSteps"
          :current-substep="currentSubstep"
          :total-substeps="totalSubsteps"
          :has-validation="true"
          :validated="k1Valid"
          @prev="goPrev"
          @next="goNext"
        />
      </section>

      <!-- 步骤3: k=0.1数据测量 -->
      <section class="step-section" v-if="currentStep === 3">
        <h2>步骤3: k=0.1 电流测量</h2>
        <div class="step-content">
          <p>请按照以下接入比例测量电流值，并填入表格：</p>

          <div class="data-table">
            <h3>k=0.1 测量数据</h3>
            <table>
              <thead>
                <tr>
                  <th>接入比例</th>
                  <th>电流值 (mA)</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in k01Data" :key="index">
                  <td>{{ item.ratio }}</td>
                  <td>
                    <el-input
                      v-model.number="item.current"
                      type="number"
                      placeholder="请输入电流值"
                      :min="0"
                      :max="50"
                      size="small"
                    />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <ExperimentNavigation
          :current-step="currentStep"
          :total-steps="totalSteps"
          :current-substep="currentSubstep"
          :total-substeps="totalSubsteps"
          :has-validation="true"
          :validated="k01Valid"
          @prev="goPrev"
          @next="goNext"
        />
      </section>

      <!-- 步骤4: 数据分析 -->
      <section class="step-section" v-if="currentStep === 4">
        <h2>步骤4: 数据分析</h2>
        <div class="step-content">
          <!-- 图表显示 -->
          <div class="chart-container" v-if="plotImageUrl">
            <h3>电流-接入比例关系图</h3>
            <img :src="plotImageUrl" alt="实验数据图表" class="chart-image" />
          </div>

          <!-- AI分析结果 -->
          <div class="analysis-result" v-if="analysisResult">
            <h3>实验分析</h3>
            <div class="analysis-content">
              <p><strong>分析结果：</strong>{{ analysisResult }}</p>
              <p><strong>实验状态：</strong>
                <el-tag :type="isPassed ? 'success' : 'warning'">
                  {{ isPassed ? '通过' : '需要改进' }}
                </el-tag>
              </p>
            </div>
          </div>

          <el-button type="primary" @click="generateAnalysis" :loading="analyzing">
            {{ analyzing ? '分析中...' : '生成分析' }}
          </el-button>
        </div>
        <ExperimentNavigation
          :current-step="currentStep"
          :total-steps="totalSteps"
          :current-substep="currentSubstep"
          :total-substeps="totalSubsteps"
          :has-validation="true"
          :validated="Boolean(analysisResult)"
          @prev="goPrev"
          @next="goNext"
        />
      </section>

      <!-- 步骤5: 提交实验 -->
      <section class="step-section" v-if="currentStep === 5">
        <h2>步骤5: 提交实验报告</h2>
        <div class="step-content">
          <div class="submit-form">
            <h3>学生信息</h3>
            <el-form :model="studentInfo" label-width="100px">
              <el-form-item label="学号">
                <el-input v-model="studentInfo.studentId" placeholder="请输入学号" />
              </el-form-item>
              <el-form-item label="姓名">
                <el-input v-model="studentInfo.name" placeholder="请输入姓名" />
              </el-form-item>
            </el-form>
          </div>

          <div class="experiment-summary">
            <h3>实验总结</h3>
            <p><strong>实验状态：</strong>
              <el-tag :type="isPassed ? 'success' : 'warning'">
                {{ isPassed ? '实验通过' : '需要改进' }}
              </el-tag>
            </p>
            <p><strong>完成时间：</strong>{{ new Date().toLocaleString() }}</p>
          </div>
        </div>
        <ExperimentNavigation
          :current-step="currentStep"
          :total-steps="totalSteps"
          :current-substep="currentSubstep"
          :total-substeps="totalSubsteps"
          :show-next="true"
          :show-prev="true"
          @prev="goPrev"
          @next="submitExperiment"
        />
      </section>
    </main>

    <!-- 步骤指示器 -->
    <footer class="step-indicator">
      <div class="steps">
        <div
          v-for="step in 5"
          :key="step"
          :class="['step-item', { active: currentStep === step, completed: currentStep > step }]"
        >
          {{ step }}
        </div>
      </div>
      <p>步骤 {{ currentStep }} / 5</p>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()
import StepIndicator from '@/components/StepIndicator.vue'
import ExperimentNavigation from '@/components/ExperimentNavigation.vue'

// Unified API base from Vite env (injected via vite.config or .env.development)
const API_BASE = (import.meta.env.VITE_API_BASE as string) || 'http://localhost:8000'

// 通用步骤与子步骤定义（便于未来其它实验复用）
const steps = [
  { title: '器材准备与安全检查', substeps: [
    { title: '准备稳压电源' },
    { title: '检查电流表' },
    { title: '准备电阻箱' },
    { title: '准备滑线变阻器' },
    { title: '准备开关' },
    { title: '准备导线' }
  ] },
  { title: '电路连接' },
  { title: 'k=1 测量准备', substeps: [
    { title: '设置 k=1' },
    { title: '设置电源电压' },
    { title: '量程确认' }
  ] },
  { title: 'k=1 数据测量' },
  { title: 'k=0.1 测量准备', substeps: [
    { title: '断开电路' },
    { title: '电压调零' },
    { title: '设置 k=0.1' },
    { title: '设置初始位置' },
    { title: '闭合开关' },
    { title: '调节电压' },
    { title: '记录初始电流' }
  ] },
  { title: 'k=0.1 数据测量' },
  { title: '数据分析与图形生成' }
]

const totalSteps = steps.length
const currentSubstep = ref(1)

const totalSubsteps = computed(() => {
  const sub = steps[currentStep.value - 1]?.substeps
  return Array.isArray(sub) ? sub.length : 0
})

// 用于表格验证
const k1Valid = computed(() => k1Data.value.every(r => r.current !== null && !Number.isNaN(r.current)))
const k01Valid = computed(() => k01Data.value.every(r => r.current !== null && !Number.isNaN(r.current)))

// 导航
const goPrev = () => {
  if (totalSubsteps.value > 0 && currentSubstep.value > 1) {
    currentSubstep.value -= 1
    return
  }
  if (currentStep.value > 1) {
    currentStep.value -= 1
    // 如果上一主步骤有子步骤，从1开始
    currentSubstep.value = steps[currentStep.value - 1]?.substeps ? 1 : 1
  }
}

const goNext = () => {
  if (totalSubsteps.value > 0 && currentSubstep.value < totalSubsteps.value) {
    currentSubstep.value += 1
    return
  }
  if (currentStep.value < totalSteps) {
    currentStep.value += 1
    currentSubstep.value = steps[currentStep.value - 1]?.substeps ? 1 : 1
  }
}


// 当前步骤
const currentStep = ref(1)

// 实验数据
const k1Data = ref([
  { ratio: 0.0, current: null },
  { ratio: 0.1, current: null },
  { ratio: 0.2, current: null },
  { ratio: 0.3, current: null },
  { ratio: 0.4, current: null },
  { ratio: 0.5, current: null },
  { ratio: 0.6, current: null },
  { ratio: 0.7, current: null },
  { ratio: 0.8, current: null },
  { ratio: 0.9, current: null },
  { ratio: 1.0, current: null }
])

const k01Data = ref([
  { ratio: 0.0, current: null },
  { ratio: 0.1, current: null },
  { ratio: 0.2, current: null },
  { ratio: 0.3, current: null },
  { ratio: 0.4, current: null },
  { ratio: 0.5, current: null },
  { ratio: 0.6, current: null },
  { ratio: 0.7, current: null },
  { ratio: 0.8, current: null },
  { ratio: 0.9, current: null },
  { ratio: 1.0, current: null }
])

// 学生信息
const studentInfo = ref({
  studentId: '',
  name: ''
})

// 分析结果
const plotImageUrl = ref('')
const analysisResult = ref('')
const isPassed = ref(false)
const analyzing = ref(false)
const submitting = ref(false)


const generateAnalysis = async () => {
  analyzing.value = true
  try {
    // 1) 生成图形（无需登录）
    const plotResp = await fetch(`${API_BASE}/api/experiments/plot`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        experiment_code: 'current_control_circuit',
        data: {
          k1Data: k1Data.value,
          k01Data: k01Data.value
        }
      })
    })
    const plotJson = await plotResp.json()
    if (plotJson && plotJson.plot_data) {
      plotImageUrl.value = `data:image/png;base64,${plotJson.plot_data}`
    }

    // 2) AI分析（后端返回模拟通过）
    const analyzeResp = await fetch(`${API_BASE}/api/experiments/analyze`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        experiment_code: 'current_control_circuit',
        data: {
          k1Data: k1Data.value,
          k01Data: k01Data.value
        }
      })
    })
    const analyzeJson = await analyzeResp.json()
    analysisResult.value = analyzeJson?.analysis_result || '模拟分析完成'
    isPassed.value = Boolean(analyzeJson?.is_passed)

    ElMessage.success('分析完成')
  } catch (error) {
    console.error('分析失败:', error)
    ElMessage.error('分析失败，请重试')
  } finally {
    analyzing.value = false
  }
}

const submitExperiment = async () => {
  submitting.value = true
  try {
    const response = await fetch(`${API_BASE}/api/experiments/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        student_id: studentInfo.value.studentId,
        name: studentInfo.value.name,
        experiment_type_code: 'current_control_circuit',
        submission_data: {
          k1Data: k1Data.value,
          k01Data: k01Data.value
        }
      })
    })

    await response.json()

    ElMessage.success('实验报告提交成功！')

    // 3秒后返回首页
    setTimeout(() => {
      router.push('/')
    }, 3000)

  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请重试')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
/* 基础布局 */
.experiment-page {
  min-height: 100vh;
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
  background: #f5f5f5;
}

/* 实验标题 */
.experiment-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.experiment-header h1 {
  font-size: 2.2rem;
  margin-bottom: 12px;
  font-weight: 600;
}

.experiment-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

/* 实验内容 */
.experiment-content {
  margin-bottom: 30px;
}

.step-section {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.step-section h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.5rem;
  border-bottom: 2px solid #667eea;
  padding-bottom: 10px;
}

.step-content {
  margin-bottom: 30px;
}

.step-content h3 {
  color: #555;
  margin: 20px 0 12px 0;
  font-size: 1.2rem;
}

.step-content ul {
  margin: 12px 0;
  padding-left: 20px;
}

.step-content li {
  margin: 8px 0;
  color: #666;
  line-height: 1.5;
}

/* 数据表格 */
.data-table {
  margin: 20px 0;
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 12px;
}

.data-table th,
.data-table td {
  padding: 12px;
  text-align: center;
  border: 1px solid #ddd;
}

.data-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.data-table td {
  background: white;
}

/* 图表容器 */
.chart-container {
  text-align: center;
  margin: 20px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.chart-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 分析结果 */
.analysis-result {
  margin: 20px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.analysis-content p {
  margin: 8px 0;
  color: #333;
}

/* 提交表单 */
.submit-form {
  margin: 20px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.experiment-summary {
  margin: 20px 0;
  padding: 20px;
  background: #f0f8ff;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.experiment-summary p {
  margin: 8px 0;
  color: #333;
}

/* 步骤操作 */
.step-actions {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

/* 步骤指示器 */
.step-indicator {
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.steps {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 12px;
}

.step-item {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e0e0e0;
  color: #666;
  font-weight: 600;
  transition: all 0.3s ease;
}

.step-item.active {
  background: #667eea;
  color: white;
}

.step-item.completed {
  background: #4caf50;
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .experiment-page {
    padding: 15px;
  }

  .experiment-header {
    padding: 20px;
  }

  .experiment-header h1 {
    font-size: 1.8rem;
  }

  .step-section {
    padding: 20px;
  }

  .step-actions {
    flex-direction: column;
  }

  .steps {
    gap: 12px;
  }

  .step-item {
    width: 35px;
    height: 35px;
  }
}

@media (max-width: 480px) {
  .experiment-page {
    padding: 10px;
  }

  .experiment-header {
    padding: 15px;
  }

  .experiment-header h1 {
    font-size: 1.5rem;
  }

  .step-section {
    padding: 15px;
  }

  .data-table th,
  .data-table td {
    padding: 8px;
    font-size: 0.9rem;
  }
}
</style>
