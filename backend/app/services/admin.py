"""
管理员服务
"""

from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Optional

from app.models.user import Student, Admin, Class, Group
from app.models.experiment import ExperimentRecord, ExperimentType, ExperimentStatus


class AdminService:
    """管理员服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_students(self, class_id: Optional[int] = None) -> List[Student]:
        """获取学生列表"""
        query = self.db.query(Student)
        
        if class_id:
            query = query.filter(Student.class_id == class_id)
        
        return query.all()
    
    def get_classes(self) -> List[Class]:
        """获取班级列表"""
        return self.db.query(Class).all()
    
    def get_groups(self, class_id: Optional[int] = None) -> List[Group]:
        """获取分组列表"""
        query = self.db.query(Group)
        
        if class_id:
            query = query.filter(Group.class_id == class_id)
        
        return query.all()
    
    def get_experiment_records(
        self,
        student_id: Optional[int] = None,
        experiment_type_id: Optional[int] = None,
        status: Optional[str] = None
    ) -> List[ExperimentRecord]:
        """获取实验记录"""
        query = self.db.query(ExperimentRecord)
        
        if student_id:
            query = query.filter(ExperimentRecord.student_id == student_id)
        
        if experiment_type_id:
            query = query.filter(ExperimentRecord.experiment_type_id == experiment_type_id)
        
        if status:
            query = query.filter(ExperimentRecord.status == status)
        
        return query.all()
    
    def get_experiment_statistics(self) -> List[dict]:
        """获取实验统计信息"""
        # 查询每个实验类型的统计信息
        stats = self.db.query(
            ExperimentType.name,
            func.count(ExperimentRecord.id).label('total_submissions'),
            func.sum(func.case([(ExperimentRecord.is_passed == True, 1)], else_=0)).label('passed_count'),
            func.sum(func.case([(ExperimentRecord.is_passed == False, 1)], else_=0)).label('failed_count'),
            func.sum(func.case([(ExperimentRecord.is_passed == None, 1)], else_=0)).label('pending_count')
        ).outerjoin(
            ExperimentRecord, ExperimentType.id == ExperimentRecord.experiment_type_id
        ).group_by(ExperimentType.id, ExperimentType.name).all()
        
        result = []
        for stat in stats:
            total = stat.total_submissions or 0
            passed = stat.passed_count or 0
            failed = stat.failed_count or 0
            pending = stat.pending_count or 0
            
            pass_rate = (passed / total * 100) if total > 0 else 0
            
            result.append({
                "experiment_name": stat.name,
                "total_submissions": total,
                "passed_count": passed,
                "failed_count": failed,
                "pending_count": pending,
                "pass_rate": round(pass_rate, 2)
            })
        
        return result
