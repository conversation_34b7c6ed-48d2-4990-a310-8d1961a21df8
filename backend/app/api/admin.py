"""
管理员相关API路由
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional
import logging

from app.utils.database import get_db
from app.utils.auth import get_current_user, require_admin
from app.models.user import Student, Admin, Class, Group
from app.models.experiment import ExperimentRecord
from app.services.admin import AdminService

logger = logging.getLogger(__name__)

router = APIRouter()


class StudentManagementResponse(BaseModel):
    """学生管理响应模型"""
    id: int
    student_id: str
    name: str
    class_name: str
    group_name: Optional[str]
    email: Optional[str]
    is_active: bool
    experiment_count: int


class ClassResponse(BaseModel):
    """班级响应模型"""
    id: int
    name: str
    code: str
    department: Optional[str]
    grade: Optional[int]
    student_count: int


class ExperimentStatistics(BaseModel):
    """实验统计模型"""
    experiment_name: str
    total_submissions: int
    passed_count: int
    failed_count: int
    pending_count: int
    pass_rate: float


@router.get("/students", response_model=List[StudentManagementResponse])
async def get_all_students(
    class_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: dict = Depends(require_admin)
):
    """获取所有学生信息"""
    try:
        admin_service = AdminService(db)
        students = admin_service.get_students(class_id=class_id)
        
        return [
            StudentManagementResponse(
                id=student.id,
                student_id=student.student_id,
                name=student.name,
                class_name=student.class_info.name,
                group_name=student.group_info.name if student.group_info else None,
                email=student.email,
                is_active=student.is_active,
                experiment_count=len(student.experiment_records)
            )
            for student in students
        ]
        
    except Exception as e:
        logger.error(f"获取学生列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取学生列表失败"
        )


@router.get("/classes", response_model=List[ClassResponse])
async def get_all_classes(
    db: Session = Depends(get_db),
    current_user: dict = Depends(require_admin)
):
    """获取所有班级信息"""
    try:
        admin_service = AdminService(db)
        classes = admin_service.get_classes()
        
        return [
            ClassResponse(
                id=cls.id,
                name=cls.name,
                code=cls.code,
                department=cls.department,
                grade=cls.grade,
                student_count=len(cls.students)
            )
            for cls in classes
        ]
        
    except Exception as e:
        logger.error(f"获取班级列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取班级列表失败"
        )


@router.get("/statistics/experiments", response_model=List[ExperimentStatistics])
async def get_experiment_statistics(
    db: Session = Depends(get_db),
    current_user: dict = Depends(require_admin)
):
    """获取实验统计信息"""
    try:
        admin_service = AdminService(db)
        statistics = admin_service.get_experiment_statistics()
        
        return statistics
        
    except Exception as e:
        logger.error(f"获取实验统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取实验统计失败"
        )


@router.get("/records")
async def get_all_experiment_records(
    student_id: Optional[int] = None,
    experiment_type_id: Optional[int] = None,
    status: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: dict = Depends(require_admin)
):
    """获取所有实验记录"""
    try:
        admin_service = AdminService(db)
        records = admin_service.get_experiment_records(
            student_id=student_id,
            experiment_type_id=experiment_type_id,
            status=status
        )
        
        return {
            "records": [record.to_dict() for record in records],
            "total": len(records)
        }
        
    except Exception as e:
        logger.error(f"获取实验记录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取实验记录失败"
        )
