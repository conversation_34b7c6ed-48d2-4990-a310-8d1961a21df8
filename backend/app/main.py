"""
大学物理实验基础指导平台 - FastAPI 主应用
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import logging
from loguru import logger

from app.api import auth, experiments, admin, students
from app.utils.database import init_db, close_db
from app.utils.config import settings


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化数据库（容错处理）
    logger.info("正在初始化数据库连接...")
    try:
        await init_db()
        logger.info("数据库连接初始化完成")
    except Exception as e:
        logger.warning(f"数据库连接失败，但继续启动服务: {e}")
        logger.info("API 服务将在无数据库模式下运行（仅支持绘图和模拟分析）")

    yield

    # 关闭时清理资源
    logger.info("正在关闭数据库连接...")
    try:
        await close_db()
        logger.info("数据库连接已关闭")
    except Exception as e:
        logger.warning(f"关闭数据库连接时出错: {e}")


# 创建FastAPI应用实例
app = FastAPI(
    title="大学物理实验基础指导平台",
    description="支持多种物理实验的在线指导、数据收集、图形绘制和AI分析的教学平台",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 配置可信主机中间件
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)


# 全局异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "status_code": exc.status_code
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": "服务器内部错误",
            "status_code": 500
        }
    )


# 根路由
@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "大学物理实验基础指导平台 API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }


# 健康检查路由
@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "message": "服务运行正常"
    }


# 注册API路由
app.include_router(
    auth.router,
    prefix="/api/auth",
    tags=["认证"]
)

app.include_router(
    experiments.router,
    prefix="/api/experiments",
    tags=["实验"]
)

app.include_router(
    students.router,
    prefix="/api/students",
    tags=["学生"]
)

app.include_router(
    admin.router,
    prefix="/api/admin",
    tags=["管理"]
)


if __name__ == "__main__":
    import uvicorn
    
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 启动服务器
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
