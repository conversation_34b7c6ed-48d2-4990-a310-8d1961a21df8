Metadata-Version: 2.1
Name: kaleido
Version: 1.0.0
Summary: Plotly graph export library
Author-email: <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
Maintainer-email: <PERSON> <<EMAIL>>
License: The MIT License (MIT)
        
        Copyright (c) Plotly, Inc
        
        Permission is hereby granted, free of charge, to any person obtaining a copy
        of this software and associated documentation files (the "Software"), to deal
        in the Software without restriction, including without limitation the rights
        to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
        copies of the Software, and to permit persons to whom the Software is
        furnished to do so, subject to the following conditions:
        
        The above copyright notice and this permission notice shall be included in
        all copies or substantial portions of the Software.
        
        THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
        IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
        FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
        AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
        LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
        OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
        THE SOFTWARE.
        
Project-URL: Homepage, https://github.com/plotly/kaleido
Project-URL: Repository, https://github.com/plotly/kaleido
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE.md
Requires-Dist: choreographer>=1.0.5
Requires-Dist: logistro>=1.0.8
Requires-Dist: orjson>=3.10.15
Requires-Dist: packaging


<div align="center">
  <a href="https://dash.plotly.com/project-maintenance">
    <img src="https://dash.plotly.com/assets/images/maintained-by-plotly.png"
    width="400px" alt="Maintained by Plotly">
  </a>
</div>

# Overview
Kaleido is a cross-platform Python library for generating static images (e.g. png, svg, pdf, etc.) for Plotly.js, to be used by Plotly.py.

## Installation

Kaleido can be installed from [PyPI](https://pypi.org/project/kaleido) using `pip`:

```bash
$ pip install kaleido --upgrade
```

As of version 1.0.0, Kaleido requires Chrome to be installed. If you already have Chrome on your system, Kaleido should find it; otherwise, you can install a compatible Chrome version using the `kaleido_get_chrome` command:

```bash
$ kaleido_get_chrome
```

or function in Python:

```python
import kaleido
kaleido.get_chrome_sync()
```

## Migrating from v0 to v1

Kaleido v1 introduces a new API. If you're currently using v0, you'll need to make changes to your code and environment where you are running Kaleido.

- If using Kaleido v1 with Plotly.py, you will need to install Plotly.py v6.1.1 or later.
- Chrome is no longer included with Kaleido. Kaleido will look for an existing Chrome installation, but also provides commands for installing Chrome. If you don't have Chrome, you'll need to install it. See the installation section above for instructions.
- If your code uses Kaleido directly: `kaleido.scopes.plotly` has been removed in v1. Kaleido v1 provides `write_fig` and `write_fig_sync` for exporting Plotly figures.
```
from kaleido import write_fig_sync
import plotly.graph_objects as go

fig = go.Figure(data=[go.Scatter(y=[1, 3, 2])])
kaleido.write_fig_sync(fig, path="figure.png")
```

## Development guide

Below are examples of how to use Kaleido directly in your Python program.

If you want to export images of Plotly charts, it's not necessary to call Kaleido directly; you can use functions in the Plotly library. [See the Plotly documentation for instructions.](https://plotly.com/python/static-image-export/)

### Usage examples

```python
import kaleido

async with kaleido.Kaleido(n=4, timeout=90) as k:
  # n is number of processes
  await k.write_fig(fig, path="./", opts={"format":"jpg"})

# other `kaleido.Kaleido` arguments:
# page:  Change library version (see PageGenerators below)

# `Kaleido.write_fig()` arguments:
# - fig:       A single plotly figure or an iterable.
# - path:      A directory (names auto-generated based on title)
#              or a single file.
# - opts:      A dictionary with image options:
#              `{"scale":..., "format":..., "width":..., "height":...}`
# - error_log: If you pass a list here, image-generation errors will be appended
#              to the list and generation continues. If left as `None`, the
#              first error will cause failure.

# You can also use Kaleido.write_fig_from_object:
  await k.write_fig_from_object(fig_objects, error_log)
# where `fig_objects` is a dict to be expanded to the fig, path, opts arguments.
```

There are shortcut functions which can be used to generate images without creating a `Kaleido()` object:

```python
import asyncio
import kaleido
asyncio.run(
  kaleido.write_fig(
    fig,
    path="./",
    n=4
  )
)
```

### PageGenerators

The `page` argument takes a `kaleido.PageGenerator()` to customize versions.
Normally, kaleido looks for an installed plotly as uses that version. You can pass
`kaleido.PageGenerator(force_cdn=True)` to force use of a CDN version of plotly (the
default if plotly is not installed).
```
my_page = kaleido.PageGenerator(
  plotly="A fully qualified link to plotly (https:// or file://)",
  mathjax=False # no mathjax, or another fully quality link
  others=["a list of other script links to include"]
)
```

