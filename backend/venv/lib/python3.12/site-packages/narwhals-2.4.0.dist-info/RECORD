narwhals-2.4.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
narwhals-2.4.0.dist-info/METADATA,sha256=0Ps2jugT8dTuEdI8THzFT0bdDAqEQXtBAhmNkSHaY5c,11307
narwhals-2.4.0.dist-info/RECORD,,
narwhals-2.4.0.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
narwhals-2.4.0.dist-info/licenses/LICENSE.md,sha256=heMD6hta6RzeBucppx59AUCgr_ukRY0ABj0bcrN3mKs,1071
narwhals/__init__.py,sha256=FGVSkIo-2xnlr-PAnUz9_lUsoAVLmwSko7vvKt1dhwM,3206
narwhals/__pycache__/__init__.cpython-312.pyc,,
narwhals/__pycache__/_constants.cpython-312.pyc,,
narwhals/__pycache__/_duration.cpython-312.pyc,,
narwhals/__pycache__/_enum.cpython-312.pyc,,
narwhals/__pycache__/_exceptions.cpython-312.pyc,,
narwhals/__pycache__/_expression_parsing.cpython-312.pyc,,
narwhals/__pycache__/_namespace.cpython-312.pyc,,
narwhals/__pycache__/_translate.cpython-312.pyc,,
narwhals/__pycache__/_typing.cpython-312.pyc,,
narwhals/__pycache__/_typing_compat.cpython-312.pyc,,
narwhals/__pycache__/_utils.cpython-312.pyc,,
narwhals/__pycache__/dataframe.cpython-312.pyc,,
narwhals/__pycache__/dependencies.cpython-312.pyc,,
narwhals/__pycache__/dtypes.cpython-312.pyc,,
narwhals/__pycache__/exceptions.cpython-312.pyc,,
narwhals/__pycache__/expr.cpython-312.pyc,,
narwhals/__pycache__/expr_cat.cpython-312.pyc,,
narwhals/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/__pycache__/expr_list.cpython-312.pyc,,
narwhals/__pycache__/expr_name.cpython-312.pyc,,
narwhals/__pycache__/expr_str.cpython-312.pyc,,
narwhals/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/__pycache__/functions.cpython-312.pyc,,
narwhals/__pycache__/group_by.cpython-312.pyc,,
narwhals/__pycache__/schema.cpython-312.pyc,,
narwhals/__pycache__/selectors.cpython-312.pyc,,
narwhals/__pycache__/series.cpython-312.pyc,,
narwhals/__pycache__/series_cat.cpython-312.pyc,,
narwhals/__pycache__/series_dt.cpython-312.pyc,,
narwhals/__pycache__/series_list.cpython-312.pyc,,
narwhals/__pycache__/series_str.cpython-312.pyc,,
narwhals/__pycache__/series_struct.cpython-312.pyc,,
narwhals/__pycache__/this.cpython-312.pyc,,
narwhals/__pycache__/translate.cpython-312.pyc,,
narwhals/__pycache__/typing.cpython-312.pyc,,
narwhals/__pycache__/utils.cpython-312.pyc,,
narwhals/_arrow/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_arrow/__pycache__/__init__.cpython-312.pyc,,
narwhals/_arrow/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_arrow/__pycache__/expr.cpython-312.pyc,,
narwhals/_arrow/__pycache__/group_by.cpython-312.pyc,,
narwhals/_arrow/__pycache__/namespace.cpython-312.pyc,,
narwhals/_arrow/__pycache__/selectors.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_cat.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_dt.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_list.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_str.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_struct.cpython-312.pyc,,
narwhals/_arrow/__pycache__/typing.cpython-312.pyc,,
narwhals/_arrow/__pycache__/utils.cpython-312.pyc,,
narwhals/_arrow/dataframe.py,sha256=v2x502jBYsuRCsLZvVrvVDnAa_V75JA4joTj0MW60SI,29247
narwhals/_arrow/expr.py,sha256=j00YfKCWouDbNBVpcxQWUNu5s-ONGxeQz1EhLx-FkzA,6390
narwhals/_arrow/group_by.py,sha256=SkDRYpKaZXkwxtC-5s1yinBSgVgj2KoAiFFpjSvo9Fo,6458
narwhals/_arrow/namespace.py,sha256=9YWEgy-LN1CgfHHcR1oXTaL2rdsgXIhLWLNABoooLZg,11966
narwhals/_arrow/selectors.py,sha256=qIfCnMNlQ5svQzGaB-DV5YE4xSaUaVzElTPYJl_0BJc,1128
narwhals/_arrow/series.py,sha256=1zgpzxUm9iwVwn8jdjTeNBHrDl-JZFaw--IThGjvPBw,45006
narwhals/_arrow/series_cat.py,sha256=vvNlPaHHcA-ORzh_79-oY03wt6aIg1rLI0At8FXr2Ok,598
narwhals/_arrow/series_dt.py,sha256=tTJg3Kxde356LNmqfHOHseWkNofyigFu7s17EtEYTQs,8922
narwhals/_arrow/series_list.py,sha256=hhIE7wZGVQs-J9iX-RyP4sedZ413fStDDj2aW006ALI,647
narwhals/_arrow/series_str.py,sha256=RKtxW9FuZ2R-Qyj4iqP9qPF5u_QAXufzEYRJHImrRjI,4491
narwhals/_arrow/series_struct.py,sha256=85pQSUqOdeMyjsnjaSr_4YBC2HRGD-dsnNy2tPveJRM,410
narwhals/_arrow/typing.py,sha256=TmgG8eqF4uCRW5NFzWTiBvlUGvD46govtIC8gRyrkmA,2286
narwhals/_arrow/utils.py,sha256=KHNaGBGuA1wvrsCrPWR7xv9XaO0iXHgJPuaYrG-PfRI,16121
narwhals/_compliant/__init__.py,sha256=NIrlDmo6XGrEuJkHvxtPTntxJmVzn1pku31P06cI48c,2460
narwhals/_compliant/__pycache__/__init__.cpython-312.pyc,,
narwhals/_compliant/__pycache__/any_namespace.cpython-312.pyc,,
narwhals/_compliant/__pycache__/column.cpython-312.pyc,,
narwhals/_compliant/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_compliant/__pycache__/expr.cpython-312.pyc,,
narwhals/_compliant/__pycache__/group_by.cpython-312.pyc,,
narwhals/_compliant/__pycache__/namespace.cpython-312.pyc,,
narwhals/_compliant/__pycache__/selectors.cpython-312.pyc,,
narwhals/_compliant/__pycache__/series.cpython-312.pyc,,
narwhals/_compliant/__pycache__/typing.cpython-312.pyc,,
narwhals/_compliant/__pycache__/when_then.cpython-312.pyc,,
narwhals/_compliant/__pycache__/window.cpython-312.pyc,,
narwhals/_compliant/any_namespace.py,sha256=kXBY2yAN8Oz5E7k-vlq_aQHwHVEmJtYZbL_BgG089xo,3698
narwhals/_compliant/column.py,sha256=mQdztLvvfOi0Su8j2Fh03MOXcphjWL3K2wB9hSHEa-o,7449
narwhals/_compliant/dataframe.py,sha256=bTuQKeKCiNYdYEsrc16I9vFQ-_EK2q2125srzldu-p8,15233
narwhals/_compliant/expr.py,sha256=l9X5_Kzv_2yNTSzn7bW8ETr2HPfiqPOEhU3I6vY7jCA,41246
narwhals/_compliant/group_by.py,sha256=78xvZ3Zryh6EU25UHk5vrZbi9a6xCThBTXfBLgY0uHk,6885
narwhals/_compliant/namespace.py,sha256=wy1mEA221qlDoYZdRFC6luUAuaJHMJje1gqeEEJRGtw,8304
narwhals/_compliant/selectors.py,sha256=0TEz_n8rHv-0wdqs-WtdwvKUACPzMeH40hLhhV2xthw,11824
narwhals/_compliant/series.py,sha256=cJ-B-26UuT0oMx--29HCjp6_QSvGnTWjUwRGyMLJgsU,13629
narwhals/_compliant/typing.py,sha256=JZJJDBmffYf3il-ZXLyItM_sMhGsCTFZYow2ES0-DgM,7343
narwhals/_compliant/when_then.py,sha256=hY2O8dNYUaa-9OTUzYYfrzmQp0w13cEf0GtV1hKAiWs,4323
narwhals/_compliant/window.py,sha256=_ji4goVKkT4YPTyZa_I0N2yGmwBfB1_LDG0WSXGbmlo,505
narwhals/_constants.py,sha256=kE1KWsIky4ryabH-Z117ZtGW24ccTcreWOZJjpacO6I,1094
narwhals/_dask/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_dask/__pycache__/__init__.cpython-312.pyc,,
narwhals/_dask/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_dask/__pycache__/group_by.cpython-312.pyc,,
narwhals/_dask/__pycache__/namespace.cpython-312.pyc,,
narwhals/_dask/__pycache__/selectors.cpython-312.pyc,,
narwhals/_dask/__pycache__/utils.cpython-312.pyc,,
narwhals/_dask/dataframe.py,sha256=zTLRoHh9zf4awiU3gZGWSb9-z15Dn_K7O0Ud3tWa8Oc,18146
narwhals/_dask/expr.py,sha256=PGkKdEUJrj0OiH5WGHvnVEf5bhwnLTRzNp9egcOOPoY,25603
narwhals/_dask/expr_dt.py,sha256=7vaSQPZIWsyQsZVP0vN9_60hP6bOI0WP5UDF7jksl_Y,6886
narwhals/_dask/expr_str.py,sha256=vJpPB0EhYI7Edso2ILz8_wM0FV_nOdpWu2Bu76d-2pw,4375
narwhals/_dask/group_by.py,sha256=w-NNu2gclRKKiRDVxnDiIE6-Wm5nM7c5NMRFXxdLGoA,4906
narwhals/_dask/namespace.py,sha256=I8Kx-iJ9VI1Jrg00M-eZrDN4DsKLtCbYmClR5WWwBXk,13193
narwhals/_dask/selectors.py,sha256=FafFcfFWM6uTcKUsEeqfbmBUIgYVzH5XdN6sFUVLMKU,1148
narwhals/_dask/utils.py,sha256=qdsSkVId_G6i778nfWEl5xqb1Kaq4MjkhGmUGG0eBnY,5484
narwhals/_duckdb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_duckdb/__pycache__/__init__.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_list.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/group_by.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/namespace.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/selectors.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/series.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/typing.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/utils.cpython-312.pyc,,
narwhals/_duckdb/dataframe.py,sha256=erot6PCYdvmFYYAoGXJpFEBsK64UZVQPV80sPUY-4Kc,20334
narwhals/_duckdb/expr.py,sha256=bz8mMups5LEeZTC32tizALGQgORzkXnT1BPUc2vdql8,10617
narwhals/_duckdb/expr_dt.py,sha256=QoNbABk0aIuRNyIa790HvisfB177_Ds1H_xMiZWNnHM,4990
narwhals/_duckdb/expr_list.py,sha256=gXPHQZ3oqliATMLye7JugEar-SKOTliCYkfjKv6KZBM,1326
narwhals/_duckdb/expr_str.py,sha256=M7UTLjnHI66I7XYGECORpsJwrrYaYUxyesK2NqGGuok,999
narwhals/_duckdb/expr_struct.py,sha256=eN06QA1JS6wjAt7_AZzW3xoztHM_hoadlFUl_hwsEiE,576
narwhals/_duckdb/group_by.py,sha256=nuueeiJYRcs31Ja973VvtLbWM2wnms0GYL7kAHDeju0,1123
narwhals/_duckdb/namespace.py,sha256=6iztE4lAMTwZRm_HAj2CbvOYv7UPx_fMzymFRZ6EJ3g,5892
narwhals/_duckdb/selectors.py,sha256=yA16Z-MlJUJBjOu0XI9qVO4Zx7L_T5FN2DQqNAYhu-o,1033
narwhals/_duckdb/series.py,sha256=xBpuPUnSSIQ1vYEKjHQFZN7ix1ZyMwSchliDPpkf3Wk,1397
narwhals/_duckdb/typing.py,sha256=gO_Odyinkn4QZY_TU4uuzda6mbeo38glOOUUripcWgg,454
narwhals/_duckdb/utils.py,sha256=VRFYNhgrjsNwyY2Dv-iLH3S_5GJklr2ixxB2TsZHw1o,13718
narwhals/_duration.py,sha256=WGzj3FVcC2KogqRhNeim3YDIwUn8HkXQHAljtvHrjwQ,3139
narwhals/_enum.py,sha256=sUR-04yIwjAMsX5eelKnc1UKXc5dBoj1do0krubAE04,1192
narwhals/_exceptions.py,sha256=OhT0MiQbcw_wE85Bl1YYZJjvtlX0rJMNUoZtKNCjTq8,1928
narwhals/_expression_parsing.py,sha256=-cekls62DXha7elyCLu52X0erZeK1mNgQYOjNaoog0A,22904
narwhals/_ibis/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_ibis/__pycache__/__init__.cpython-312.pyc,,
narwhals/_ibis/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr_list.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/_ibis/__pycache__/group_by.cpython-312.pyc,,
narwhals/_ibis/__pycache__/namespace.cpython-312.pyc,,
narwhals/_ibis/__pycache__/selectors.cpython-312.pyc,,
narwhals/_ibis/__pycache__/series.cpython-312.pyc,,
narwhals/_ibis/__pycache__/utils.cpython-312.pyc,,
narwhals/_ibis/dataframe.py,sha256=6w-udOQAYsuIZXxBoSpKwPt8nfbg9M8uLBqUQNe2CLM,16275
narwhals/_ibis/expr.py,sha256=l-3N1fbDQL7MuZJ4lRmj020NIaU-Jn2mWrfJlbkPhhQ,13068
narwhals/_ibis/expr_dt.py,sha256=0RS1B1geFYxyS5RplyG9VES8mFg4tylcG75dIKczvNs,3308
narwhals/_ibis/expr_list.py,sha256=TSfb_4EKRdTFIbZ2VJ9zqXJl62ZDkivweK5BiUWFsBc,948
narwhals/_ibis/expr_str.py,sha256=K54Ch9veia8t-v9Gl_SZgL-eRynad1vufZWPAW-oKg8,2944
narwhals/_ibis/expr_struct.py,sha256=FDsa5MqcHhqPmpZIEfGBASdqxPkyImrlGTH7XUSw3cs,565
narwhals/_ibis/group_by.py,sha256=enNzAPUsA_LIwPNJ7jG_MJKyqG2HyCiesBEX3pJgJBg,1031
narwhals/_ibis/namespace.py,sha256=0hRod5QixeON0gr4XCqEJrHh3Wa3JK9_4Dz7MTJlFbI,5521
narwhals/_ibis/selectors.py,sha256=SkFxoukpKc_OjwKoKHRm8VwMaphCMUeWBJ2g_oWz3D0,961
narwhals/_ibis/series.py,sha256=CZDwDPsdELKtdr7OWmcFyGqexr33Ucfnv_RU95VJxIQ,1218
narwhals/_ibis/utils.py,sha256=rfx3LvjZY57sDRidj7DEVSQNSk8igf8QS-_tRmr2LlQ,10126
narwhals/_interchange/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_interchange/__pycache__/__init__.cpython-312.pyc,,
narwhals/_interchange/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_interchange/__pycache__/series.cpython-312.pyc,,
narwhals/_interchange/dataframe.py,sha256=FC_2WuxxI4RPUBMqcau68rkdfsxhClou4zCErx1flcw,6034
narwhals/_interchange/series.py,sha256=nSxdlOZrw3wtavS42TMR_b_EGgPBv224ioZBMo5eoC8,1651
narwhals/_namespace.py,sha256=yb0AVy4Npfn4JopkW_rVEHMXXaFusMpwDl54xZmL__M,14220
narwhals/_pandas_like/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_pandas_like/__pycache__/__init__.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/expr.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/group_by.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/namespace.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/selectors.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_cat.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_dt.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_list.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_str.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_struct.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/typing.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/utils.cpython-312.pyc,,
narwhals/_pandas_like/dataframe.py,sha256=GcGkaIh8WOE-AyQB0yRKQ9YCtlWAwEBtsnTq90Caoi8,42958
narwhals/_pandas_like/expr.py,sha256=9JhVxAzfhhrY2LpBhGWoWRrUTfEwlbO0btmfR3SgU2s,14787
narwhals/_pandas_like/group_by.py,sha256=T_o11xywuFCyKUA10wQfTyrh82wPHF3Aka29QL_d7vk,13544
narwhals/_pandas_like/namespace.py,sha256=a9PjhoTOBj2-iWz7RXLrC-smArTzC_HRpGQ10Z9_fSU,16916
narwhals/_pandas_like/selectors.py,sha256=Qf7r0H6R8cniwDwC2zlWxddsPx-AHFsZwDPQ9iCEiH8,1261
narwhals/_pandas_like/series.py,sha256=DRqqp20sREqwdsCsk2cKDJPYGc7Y9DjDkDyRVPi_lTw,43404
narwhals/_pandas_like/series_cat.py,sha256=MJwCnJ49hfnODh6JgMHOCQ2KBlTbmySU6_X4XWaqiz4,527
narwhals/_pandas_like/series_dt.py,sha256=EnNPp6StDmwDX9p3mlQw3m4pZWOp0UA1Zh5bJr-gOZE,11587
narwhals/_pandas_like/series_list.py,sha256=xc9m4c5ftCQPfiTy7EujhfNHn7KHbjBUNa-iXHdV9t8,1391
narwhals/_pandas_like/series_str.py,sha256=Wl871eCAw1X10bfreJw9Iw_pIkIpORRQMJq09ueRDlU,3982
narwhals/_pandas_like/series_struct.py,sha256=vX9HoO42vHackvVozUfp8odM9uJ4owct49ydKDnohdk,518
narwhals/_pandas_like/typing.py,sha256=Awm2YnewvdA3l_4SEwb_5AithhwBYNx1t1ajaHnvUsM,1064
narwhals/_pandas_like/utils.py,sha256=tdnpd-wOqPx1HTDQJ2rJfCYhSQ-roAfDodEj6KA0V_s,26470
narwhals/_polars/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_polars/__pycache__/__init__.cpython-312.pyc,,
narwhals/_polars/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_polars/__pycache__/expr.cpython-312.pyc,,
narwhals/_polars/__pycache__/group_by.cpython-312.pyc,,
narwhals/_polars/__pycache__/namespace.cpython-312.pyc,,
narwhals/_polars/__pycache__/series.cpython-312.pyc,,
narwhals/_polars/__pycache__/typing.cpython-312.pyc,,
narwhals/_polars/__pycache__/utils.cpython-312.pyc,,
narwhals/_polars/dataframe.py,sha256=Pa9VwvoRLFM7HSlTWjiRDpIEWSaY5agbLrSFfM5x3Jk,23358
narwhals/_polars/expr.py,sha256=BOaEXHyNIsZC2trbqvkp7bbfvsKQp7EFX7yfQQaxq4k,16087
narwhals/_polars/group_by.py,sha256=PsxQPWX7QWy2iXUWJEelC5O6mR1TKr9MB_i6O5RVxms,2373
narwhals/_polars/namespace.py,sha256=lgfcUXhdfb-uV18tYZK7Jgj74yzDEGa-CD_MpJZlX_E,10671
narwhals/_polars/series.py,sha256=J8_3pLA6YXBm-S9ZifnlQv_cqal21qC10MVFc6C1r64,26070
narwhals/_polars/typing.py,sha256=tiAgtciFZmlqqH3Q6MdQLXZEb1ajH-YbePpaKjeuqQE,786
narwhals/_polars/utils.py,sha256=hIjAce1DOez6ZEJPBbY4x_Pl4wGubdnbye5H6tE0_DA,12386
narwhals/_spark_like/__init__.py,sha256=T7_UCePrrYs1ZeMatvCUYUvQcXvrDjQ4b08_ugWIHAo,87
narwhals/_spark_like/__pycache__/__init__.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_list.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/group_by.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/namespace.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/selectors.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/utils.cpython-312.pyc,,
narwhals/_spark_like/dataframe.py,sha256=ao9cy3_mtaQeN9JRMsKImlsH4dgoYmuKQQ8TJj6KGMc,23280
narwhals/_spark_like/expr.py,sha256=exJhD-iHXB9ecUd2Hh_tbLSdumEq9srAYH_w6fxxHdk,14055
narwhals/_spark_like/expr_dt.py,sha256=1ICwI2xpTuK5PyGjiLEdfLgsgudDPEVdMGgVnM_c5Uk,7515
narwhals/_spark_like/expr_list.py,sha256=aU29eGRQ89F7z5XticN9-l7ShVJpD2Ni4rdxE9Wfd7w,1132
narwhals/_spark_like/expr_str.py,sha256=eGN7uW9BfP7-KVey-RrqFcQjZrO7WnTlU3PZPpPSzKk,1298
narwhals/_spark_like/expr_struct.py,sha256=haBDpuRhn_nGAFjMF3arhhRr6NfefNei9vEmAOa0fQI,613
narwhals/_spark_like/group_by.py,sha256=rsAhSHEoA1pHzPk--9xtKvLJbTHOtJ45ftVKUhI7KUc,1246
narwhals/_spark_like/namespace.py,sha256=rFliViMHZ03H22K9eYCPQ9B-hbiGpHnJII_0gWbOnc8,8027
narwhals/_spark_like/selectors.py,sha256=SzJPoFjyIEviSSvPRvL81o1jjQJcM-Veqb52vFU66JQ,1086
narwhals/_spark_like/utils.py,sha256=ViTlbGgfG5koBKHtO5sCACFSFD94iTJtNGTUETOEBLw,11367
narwhals/_sql/__init__.py,sha256=T7_UCePrrYs1ZeMatvCUYUvQcXvrDjQ4b08_ugWIHAo,87
narwhals/_sql/__pycache__/__init__.cpython-312.pyc,,
narwhals/_sql/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_sql/__pycache__/expr.cpython-312.pyc,,
narwhals/_sql/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_sql/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_sql/__pycache__/group_by.cpython-312.pyc,,
narwhals/_sql/__pycache__/namespace.cpython-312.pyc,,
narwhals/_sql/__pycache__/typing.cpython-312.pyc,,
narwhals/_sql/__pycache__/when_then.cpython-312.pyc,,
narwhals/_sql/dataframe.py,sha256=RgmNIcJ2r4JIurB64vzqo0-qPUJH9kBFo4hT1gvqB0w,1491
narwhals/_sql/expr.py,sha256=Yb_iy8mt-LAdM--LLaY4TJ6zqrjdjWelmMX6wivJaNQ,29130
narwhals/_sql/expr_dt.py,sha256=CWFn7Ki3YW3XT_Hy88pdCTZs8j6jP4GpP38vgPd-vX4,1612
narwhals/_sql/expr_str.py,sha256=lZziCwu6flcjwtyUXa-2P6mGitV56tKZsnYgLD6wh9I,5234
narwhals/_sql/group_by.py,sha256=-yR5J2-Kk0awFRoFnLici00rfTK_5CxFKJSxc-Bt8Zs,1669
narwhals/_sql/namespace.py,sha256=2KRj7AnNfWEpWbjkl2X5pCya9jrq8jLwKMKFp32GFI0,2755
narwhals/_sql/typing.py,sha256=e3LkLPI4oa2IzykR7BgO9IIfCKRw0vrX4uHxPTB-uJM,487
narwhals/_sql/when_then.py,sha256=4lXcc_J_N6vHGby6kPJl1PGqLPUGbgHYuIXiYROyoW4,3636
narwhals/_translate.py,sha256=e8RjNCNX4QGJWKjM6VANDTG_bVT2VusjNfjsnkCBO3g,6112
narwhals/_typing.py,sha256=NbSFPCSdypFdKFAoJTrdRDWxmZ3XyLWPhjSBZwev6dY,6430
narwhals/_typing_compat.py,sha256=h-BtLEl7CrZ-hYLlwYTOcCdsUS3dvgvkxQTcDQ7RYmA,2516
narwhals/_utils.py,sha256=106Wm4fBMGKF0yiQVxI8lrezQEyqjLDlIrbN2iuUsIA,71254
narwhals/dataframe.py,sha256=kX8C8Pc1R49acuoZOj0ENVAXdxGO5fjjh1Jig08Smm0,133940
narwhals/dependencies.py,sha256=7fnp1kST9nA1Hl10_SsRC2iXdz3eOaR1w1Xf33JYtPc,19846
narwhals/dtypes.py,sha256=3N-VO1ZaFHCTf_JDaFjFJYQVJwYK57UD2m_cmRzCjMk,29613
narwhals/exceptions.py,sha256=9ocrbLNP7fZLqP2gV1PS9OexNhzf8h7of2wR5wi5kE0,3704
narwhals/expr.py,sha256=anCaX_Et-QuJ2lVmdWP1bIbufRHvLVGxOBIhOBiewgc,98834
narwhals/expr_cat.py,sha256=o4MhGmPoO3_DlkRB06Z3ilyqyj70mwcW3kftRayDq2s,1210
narwhals/expr_dt.py,sha256=R3F9z9FAluZBZ7ETt2lOdkMrq_OmG2YMYBpkIkGzUQc,32465
narwhals/expr_list.py,sha256=8-_L7wzxm90kcRooFW2QEdzn0FgJNMnUispBReBE5fs,6744
narwhals/expr_name.py,sha256=0QD8Yg7FKu739ENSJI2lxIGSjho89J9l9DjxeBOz9bM,4866
narwhals/expr_str.py,sha256=u3Juck5nPG9HlfnroHl1oKW_YU76rthsMUWlKWCPeYs,20055
narwhals/expr_struct.py,sha256=V_Hj3kChdcZPIlfb_Kslp5W-j-XGFcfdMFzYpZdjNWE,1742
narwhals/functions.py,sha256=nxOJ3abbCNlr4wbAQ40-MlLczEW8sVyYggq9JlrMnmA,64415
narwhals/group_by.py,sha256=7UkbFvCZ6n0rtgDovbElueEA8p4oS3i3ny_g5TGabek,7164
narwhals/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/schema.py,sha256=4yiRz6Qv373ATpN6kCFNSblUb4vMDX9USpKeJTDRrYI,12369
narwhals/selectors.py,sha256=ybbFG7Sjebr8qoMgD43O6QuHBGl52yUpGRe08L1LKyo,10759
narwhals/series.py,sha256=b0y9vQhOqSryfVaH8T7rFxPp-R-vVN6VRmsVWZpXE88,95139
narwhals/series_cat.py,sha256=KU5DMtCqi0KKVrmTfCLpgI32AGuY3MYZclNF6soh1Xc,834
narwhals/series_dt.py,sha256=a7JIMtA_Wn9ZiBa9O_-t7k6Lk-Az2AhHbQYlS1VwxAA,23051
narwhals/series_list.py,sha256=PMXSEL_Pai2ZoMcNi0KZ6WdXHlMvTVyFK600TTGhCeg,3802
narwhals/series_str.py,sha256=pdB2MI5UO7o81CeXPEiJ6v9XOhP0WPeAQACM1W_4e3Y,15400
narwhals/series_struct.py,sha256=bixxdASlxYyP1prDjMmfltVU5212a596VQZE11yszUg,930
narwhals/stable/__init__.py,sha256=b9soCkGkQzgF5jO5EdQ6IOQpnc6G6eqWmY6WwpoSjhk,85
narwhals/stable/__pycache__/__init__.cpython-312.pyc,,
narwhals/stable/v1/__init__.py,sha256=zO16qiuPxkehTMdlsOvFvzUwCnLwf2H39VZS8sex4MM,42673
narwhals/stable/v1/__pycache__/__init__.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/_dtypes.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/_namespace.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/dependencies.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/dtypes.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/selectors.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/typing.cpython-312.pyc,,
narwhals/stable/v1/_dtypes.py,sha256=7zGmarnurUTgY6DI4KQ1MSAC7B9ZZiI5Em7plb-HAEs,2700
narwhals/stable/v1/_namespace.py,sha256=gfsbT4R4aLmmdArY35LRpEHPiUeZKEEnXGiY9ypFtwE,296
narwhals/stable/v1/dependencies.py,sha256=aM0IShF4hbaaMEDRJQXvsu4RABZOdBG4QhrpJPxb7fg,5001
narwhals/stable/v1/dtypes.py,sha256=u2NFDJyCkjsK6p3K9ULJS7CoG16z0Z1MQiACTVkhkH4,1082
narwhals/stable/v1/selectors.py,sha256=xEA9bBzkpTwUanGGoFwBCcHIAXb8alwrPX1mjzE9mDM,312
narwhals/stable/v1/typing.py,sha256=7dBQVxmW6zQOjfT2N0rEddKUBD61roxhZJDJhApC0fk,6433
narwhals/stable/v2/__init__.py,sha256=YlvxU-8HtFDXVFy-43JJMbx1ORXX9i1DhgqXxH3Vrpw,40426
narwhals/stable/v2/__pycache__/__init__.cpython-312.pyc,,
narwhals/stable/v2/__pycache__/_namespace.cpython-312.pyc,,
narwhals/stable/v2/__pycache__/dependencies.cpython-312.pyc,,
narwhals/stable/v2/__pycache__/dtypes.cpython-312.pyc,,
narwhals/stable/v2/__pycache__/selectors.cpython-312.pyc,,
narwhals/stable/v2/__pycache__/typing.cpython-312.pyc,,
narwhals/stable/v2/_namespace.py,sha256=oYB5nrFGxqqTonkRx9vUanyBxGs2Yb0j7_juMyvnvWA,296
narwhals/stable/v2/dependencies.py,sha256=vpYWx_dron6wFdbQ60G06EV2UJ_LMd52LDodCrAY5Jg,86
narwhals/stable/v2/dtypes.py,sha256=iMpk2Kc1mNiQYmboOSgmiAijklSUBHSHF2LTKMKnGe8,80
narwhals/stable/v2/selectors.py,sha256=sjJL3agHd8Rgf_lWhgCmEKruhWEkwHdX32-n85OqVJU,83
narwhals/stable/v2/typing.py,sha256=mymfP7wg6FfyunGiK51I-jqEIcPA5u8gFOHN4sDoz5I,6042
narwhals/this.py,sha256=BbKcj0ReWqE01lznzKjuqq7otXONvjBevWWC5aJhQxs,1584
narwhals/translate.py,sha256=Mscw6Nh5yUgkKqWyEEst205vbfdskOUa9DlouVe2ThA,24741
narwhals/typing.py,sha256=Bh2JbyEZk0tmrzKhfnxGLFjs1W3sDTiomrXzP6rW8rQ,17164
narwhals/utils.py,sha256=2GT3XxucWI6l9r9jTwMw7Aha2G73FsSXgXNFZ3O_ZyA,223
