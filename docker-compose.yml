services:
  db:
    image: mysql:8.0
    container_name: physics_db
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
    ports:
      - "${DB_PORT}:3306"
    volumes:
      - db_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-p${DB_PASSWORD}"]
      interval: 5s
      timeout: 5s
      retries: 20

  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    container_name: physics_backend
    depends_on:
      db:
        condition: service_healthy
    env_file: .env
    environment:
      # Ensure backend connects to the Docker DB service
      DB_HOST: db
      # Force using MySQL (disable default SQLite)
      DATABASE_URL: ""
    ports:
      - "${API_PORT}:8000"
    volumes:
      - ./backend:/app/backend
      - ./backend/logs:/app/backend/logs
      - ./backend/uploads:/app/backend/uploads
    working_dir: /app/backend
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

volumes:
  db_data:

